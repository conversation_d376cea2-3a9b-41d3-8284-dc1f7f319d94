// discovery-service.js
// Discovery-related business logic

import { getFirestore, Timestamp } from 'firebase-admin/firestore';
import OpenAIConnector from '../connectors/openai-connector.js';
import InfluencersClubDiscoveryConnector from '../connectors/influencers-club-discovery-connector.js';
import { writeJsonToBucket } from '../helpers/storage-helpers.js';
import { DISCOVERY_AGENT } from '../config/constants.js';
import { DEFAULT_CLIENT_ID } from '../config/constants.js';
import { getExistingNiches, updateNichesCollection } from '../helpers/niches-helper.js';

/**
 * Discover influencers for a campaign using the Influencers Club Discovery API
 * @param {string} clientId - The client ID
 * @param {string} campaignId - The campaign ID
 * @param {Object} campaignData - The campaign data
 * @returns {Object} - The discovered influencers
 */
async function discoverInfluencers(clientId = DEFAULT_CLIENT_ID, campaignId, campaignData) {
  // Validate client ID
  if (!clientId) {
    throw new Error('Client ID is required for discovery operations');
  }

  const db = getFirestore();
  const discoveryConnector = new InfluencersClubDiscoveryConnector();

  // Discovery results will be stored in subcollection by discoveryConnector.storeDiscoveryResults()
  // No need to modify the main campaign document here

  // Prepare parameters for Discovery API
  const discoveryParams = {
    main_platform: campaignData.main_platform || 'instagram',
    influencer_description: campaignData.influencer_description,
    min_follower_count: campaignData.min_follower_count,
    max_follower_count: campaignData.max_follower_count,
    min_engagement_rate: campaignData.min_engagement_rate,
    limit: 10, // Default to 10 for production, can be overridden
    page: 1
  };

  // Add optional parameters if available
  if (campaignData.location) {
    discoveryParams.location = campaignData.location;
  }

  if (campaignData.gender) {
    discoveryParams.gender = campaignData.gender;
  }

  // Call Discovery API
  console.log(`Discovering influencers for campaign ${campaignId} using AI search...`);
  const discoveryResults = await discoveryConnector.discoverInfluencers(discoveryParams);

  // Log the response structure
  console.log(`Discovery API response received with ${discoveryResults.accounts ? discoveryResults.accounts.length : 0} accounts`);

  // Store the credits_left value from the original API response
  const creditsLeft = discoveryResults.credits_left;
  console.log(`Credits left from Discovery API: ${creditsLeft}`);

  // Log the full discovery results JSON for debugging
  console.log(`Full discovery results: ${JSON.stringify(discoveryResults)}`);

  // Format the results for the response
  // Map the accounts to the expected format with niches field
  const formattedResults = {
    similar_accounts: discoveryResults.accounts.map(account => ({
      username: account.profile.username,
      profile_url: account.profile.picture,
      profile_image_url: account.profile.picture,
      engagement_percent: account.profile.engagement_percent,
      follower_count: account.profile.followers,
      // Add empty niches array that can be populated later if needed
      niches: [],
      // Add platform information
      platform: discoveryParams.main_platform || 'instagram'
    })),
    credits_left: creditsLeft,
    total: discoveryResults.total || 0,
    limit: discoveryResults.limit || 0
  };

  // Store the discovery results in Firestore
  await discoveryConnector.storeDiscoveryResults(
    clientId,
    campaignId,
    formattedResults,
    campaignData.influencer_description
  );

  // Save to Cloud Storage for backward compatibility
  await writeJsonToBucket(`/clients/${clientId}/campaigns/${campaignId}/discovery_results.json`, formattedResults);
  await writeJsonToBucket(`/runs/${campaignId}_discovery_results.json`, formattedResults);

  // Log the formatted results that will be returned to the client
  console.log(`Formatted results being returned: ${JSON.stringify(formattedResults)}`);

  return formattedResults;
}

/**
 * Prioritize influencers and add niches
 * @param {string} clientId - The client ID
 * @param {string} campaignId - The campaign ID
 * @param {Object} campaignData - The campaign data
 * @param {Object} discoveredInfluencers - The discovered influencers
 * @returns {Object} - The prioritized influencers with niches
 */
async function prioritizeInfluencers(clientId, campaignId, campaignData, discoveredInfluencers) {
  const openai = new OpenAIConnector();

  // Get existing niches from the database
  const existingNiches = await getExistingNiches();

  // Construct the prompt
  const prioritizationPrompt = `Influencers to prioritize: [${JSON.stringify(discoveredInfluencers)}]

Campaign to rank for: [${JSON.stringify(campaignData)}]

Your tasks:
1. Rank the influencers based on their fit for the campaign
2. For each influencer, identify 5-10 specific niches that best describe their content focus

Here are the existing niches in our system: ${JSON.stringify(existingNiches)}

IMPORTANT: When assigning niches to influencers, prioritize using existing niches from the list above when they apply.
Only create new niches when none of the existing ones accurately describe an important aspect of the influencer's content.
This helps standardize our categorization while allowing for evolution when needed.`;

  // Process the prompt with OpenAI
  const prioritizedInfluencers = await openai.processAgent(DISCOVERY_AGENT, prioritizationPrompt);

  // Update niches collection with any new niches
  await updateNichesCollection(prioritizedInfluencers);

  // Store the prioritized influencers in Firestore
  const db = getFirestore();
  const prioritizedRef = db.collection('clients').doc(clientId)
    .collection('campaigns').doc(campaignId)
    .collection('prioritized_influencers').doc();

  // Prepare prioritized data
  const prioritizedData = {
    similar_accounts: prioritizedInfluencers.similar_accounts,
    created_at: Timestamp.now()
  };

  // Save to Firestore
  await prioritizedRef.set(prioritizedData);

  return prioritizedInfluencers;
}

/**
 * Retrieve existing discovery results for a campaign from Firestore
 * @param {string} clientId - The client ID
 * @param {string} campaignId - The campaign ID
 * @returns {Object} - The campaign data and discovery results in the same format as discover-from-request
 */
async function getDiscoveryResults(clientId = DEFAULT_CLIENT_ID, campaignId) {
  console.log(`[getDiscoveryResults] Retrieving discovery results for campaign: ${campaignId}, client: ${clientId}`);

  // Validate required parameters
  if (!campaignId) {
    console.error('[getDiscoveryResults] Campaign ID is required but was not provided');
    throw new Error('Campaign ID is required to retrieve discovery results');
  }

  if (!clientId) {
    console.error('[getDiscoveryResults] Client ID is required but was not provided');
    throw new Error('Client ID is required to retrieve discovery results');
  }

  const db = getFirestore();

  // Step 1: Retrieve campaign data
  console.log(`[getDiscoveryResults] Fetching campaign data for ID: ${campaignId}`);
  const campaignRef = db.collection('clients').doc(clientId).collection('campaigns').doc(campaignId);
  const campaignDoc = await campaignRef.get();

  if (!campaignDoc.exists) {
    console.error(`[getDiscoveryResults] Campaign not found: ${campaignId} for client: ${clientId}`);
    throw new Error(`Campaign not found: ${campaignId}`);
  }

  const campaignData = campaignDoc.data();
  console.log(`[getDiscoveryResults] Campaign found: ${campaignData.name}`);

  // Validate campaign data has required fields
  if (!campaignData.name) {
    console.error(`[getDiscoveryResults] Campaign data is missing required field: name`);
    throw new Error('Campaign data is incomplete - missing name field');
  }

  // Step 2: Retrieve latest discovery results from subcollection
  console.log(`[getDiscoveryResults] Fetching discovery results from subcollection`);
  const discoveryRef = db.collection('clients').doc(clientId)
    .collection('campaigns').doc(campaignId)
    .collection('influencer_discovery')
    .orderBy('timestamp', 'desc')
    .limit(1);

  const discoverySnapshot = await discoveryRef.get();

  if (discoverySnapshot.empty) {
    console.error(`[getDiscoveryResults] No discovery results found for campaign: ${campaignId}`);
    throw new Error(`No discovery results found for campaign: ${campaignId}`);
  }

  // Get the latest discovery document
  const discoveryDoc = discoverySnapshot.docs[0];
  const discoveryData = discoveryDoc.data();

  console.log(`[getDiscoveryResults] Discovery results found, timestamp: ${discoveryData.timestamp?.toDate()}`);
  console.log(`[getDiscoveryResults] Discovery results contain ${discoveryData.results?.similar_accounts?.length || 0} influencers`);

  // Step 3: Format response to match discover-from-request endpoint format
  const result = {
    campaign: {
      id: campaignId,
      name: campaignData.name,
      report_id: campaignData.report_id || campaignId,
      product_description: campaignData.product_description || '',
      influencer_gender: campaignData.influencer_gender || '',
      influencer_niche: campaignData.influencer_niche || '',
      influencer_age: campaignData.influencer_age || '',
      influencer_personality: campaignData.influencer_personality || '',
      influencer_aesthetic: campaignData.influencer_aesthetic || '',
      min_follower_count: campaignData.min_follower_count || 0,
      max_follower_count: campaignData.max_follower_count || 0,
      min_engagement_rate: campaignData.min_engagement_rate || 0,
      influencer_description: campaignData.influencer_description || '',
      main_platform: campaignData.main_platform || 'instagram',
      created_at: campaignData.created_at,
      updated_at: campaignData.updated_at,
      status: campaignData.status || 'active'
    },
    influencers: discoveryData.results?.similar_accounts || [],
    // Include discovery metadata for reference
    discovery_metadata: {
      discovery_id: discoveryDoc.id,
      timestamp: discoveryData.timestamp,
      credits_left: discoveryData.credits_left,
      total_available: discoveryData.total_available || 0,
      limit: discoveryData.limit || 0
    }
  };

  console.log(`[getDiscoveryResults] Formatted result with ${result.influencers.length} influencers`);
  console.log(`[getDiscoveryResults] Campaign: ${result.campaign.name}, Platform: ${result.campaign.main_platform}`);

  return result;
}

export {
  discoverInfluencers,
  prioritizeInfluencers,
  getDiscoveryResults
};
