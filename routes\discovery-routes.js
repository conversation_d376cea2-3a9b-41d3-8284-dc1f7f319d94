// discovery-routes.js
// Discovery-related routes

import express from 'express';
import { discoverInfluencers, getDiscoveryResults } from '../services/discovery-service.js';
import { generateCampaignBrief } from '../services/campaign-service.js';
import { DEFAULT_CLIENT_ID } from '../config/constants.js';
import { getFirestore } from 'firebase-admin/firestore';

const router = express.Router();

// The seed influencers route has been removed as it's been replaced by the Discovery API

/**
 * Discover influencers for a campaign
 * POST /api/discovery/discover
 */
router.post('/discover', async (req, res) => {
  try {
    const { clientId = DEFAULT_CLIENT_ID, campaignId, campaignData } = req.body;

    // Validate required parameters
    if (!clientId) {
      return res.status(400).json({ error: 'Client ID is required' });
    }

    if (!campaignId) {
      return res.status(400).json({ error: 'Campaign ID is required' });
    }

    if (!campaignData || !campaignData.influencer_description) {
      return res.status(400).json({ error: 'Campaign data with influencer_description is required' });
    }

    const result = await discoverInfluencers(clientId, campaignId, campaignData);
    res.status(200).json(result);
  } catch (error) {
    console.error('Error discovering influencers:', error);
    res.status(500).json({ error: 'Failed to discover influencers', message: error.message });
  }
});

/**
 * Discover influencers for a campaign by ID
 * POST /api/discovery/campaigns/:campaignId/discover
 */
router.post('/campaigns/:campaignId/discover', async (req, res) => {
  try {
    const { campaignId } = req.params;
    const { clientId = DEFAULT_CLIENT_ID } = req.body;

    // Validate client ID
    if (!clientId) {
      return res.status(400).json({ error: 'Client ID is required' });
    }

    // Get the campaign data
    const db = getFirestore();
    const campaignRef = db.collection('clients').doc(clientId).collection('campaigns').doc(campaignId);
    const campaignDoc = await campaignRef.get();

    if (!campaignDoc.exists) {
      return res.status(404).json({ error: `Campaign not found: ${campaignId}` });
    }

    const campaignData = campaignDoc.data();

    // Validate campaign data
    if (!campaignData.influencer_description) {
      return res.status(400).json({ error: 'Campaign data is missing influencer_description' });
    }

    // Discover influencers
    const result = await discoverInfluencers(clientId, campaignId, campaignData);
    res.status(200).json(result);
  } catch (error) {
    console.error('Error discovering influencers:', error);
    res.status(500).json({ error: 'Failed to discover influencers', message: error.message });
  }
});

/**
 * Discover influencers from a campaign request
 * POST /api/discovery/discover-from-request
 *
 * This endpoint combines the campaign brief generation and discovery processes:
 * 1. Takes a campaign request as input
 * 2. Generates a campaign brief
 * 3. Uses the brief to discover influencers
 * 4. Returns the discovered influencers
 */
router.post('/discover-from-request', async (req, res) => {
  try {
    const { clientId = DEFAULT_CLIENT_ID } = req.body;

    // Validate client ID
    if (!clientId) {
      return res.status(400).json({ error: 'Client ID is required' });
    }

    // Validate campaign data
    if (!req.body.campaign || !req.body.campaign.name) {
      return res.status(400).json({ error: 'Campaign data with name is required' });
    }

    console.log('Generating campaign brief from request...');

    // Step 1: Generate campaign brief
    // Ensure client_id is properly passed to generateCampaignBrief
    const briefInput = {
      ...req.body,
      client_id: clientId  // Add the extracted clientId as client_id for generateCampaignBrief
    };
    const briefResult = await generateCampaignBrief(briefInput);
    const campaignId = briefResult.campaignId;
    const campaignData = briefResult.campaignData;

    console.log(`Campaign brief generated with ID: ${campaignId}`);

    // Step 2: Discover influencers using the generated brief
    console.log('Discovering influencers based on generated brief...');
    const discoveryResult = await discoverInfluencers(clientId, campaignId, campaignData);

    // Step 3: Return the combined result
    const result = {
      campaign: {
        id: campaignId,
        ...campaignData
      },
      influencers: discoveryResult.similar_accounts || []
    };

    // Log the final combined result being returned to the client
    console.log(`Combined result from discover-from-request: ${JSON.stringify(result)}`);

    res.status(200).json(result);
  } catch (error) {
    console.error('Error in discover-from-request flow:', error);
    res.status(500).json({ error: 'Failed to process discovery request', message: error.message });
  }
});

/**
 * Get existing discovery results for a campaign
 * GET /api/discovery/campaigns/:campaignId/results
 *
 * This endpoint retrieves previously generated discovery results and campaign brief:
 * 1. Fetches the campaign data from Firestore
 * 2. Retrieves the latest discovery results from the subcollection
 * 3. Returns the data in the same format as discover-from-request endpoint
 *
 * Query Parameters:
 * - clientId (optional): Client ID, defaults to DEFAULT_CLIENT_ID
 */
router.get('/campaigns/:campaignId/results', async (req, res) => {
  try {
    const { campaignId } = req.params;
    const { clientId = DEFAULT_CLIENT_ID } = req.query;

    console.log(`[GET /campaigns/${campaignId}/results] Request received for client: ${clientId}`);

    // Validate campaign ID
    if (!campaignId) {
      console.error('[GET /campaigns/:campaignId/results] Campaign ID is missing from URL params');
      return res.status(400).json({ error: 'Campaign ID is required in URL path' });
    }

    // Validate client ID
    if (!clientId) {
      console.error('[GET /campaigns/:campaignId/results] Client ID is required');
      return res.status(400).json({ error: 'Client ID is required' });
    }

    console.log(`[GET /campaigns/${campaignId}/results] Calling getDiscoveryResults service...`);

    // Retrieve discovery results using the service function
    const result = await getDiscoveryResults(clientId, campaignId);

    console.log(`[GET /campaigns/${campaignId}/results] Successfully retrieved results for campaign: ${result.campaign.name}`);
    console.log(`[GET /campaigns/${campaignId}/results] Returning ${result.influencers.length} influencers`);

    res.status(200).json(result);
  } catch (error) {
    console.error(`[GET /campaigns/:campaignId/results] Error retrieving discovery results:`, error);

    // Handle specific error cases with appropriate HTTP status codes
    if (error.message.includes('Campaign not found')) {
      return res.status(404).json({
        error: 'Campaign not found',
        message: error.message,
        campaignId: req.params.campaignId
      });
    }

    if (error.message.includes('No discovery results found')) {
      return res.status(404).json({
        error: 'No discovery results found',
        message: error.message,
        campaignId: req.params.campaignId
      });
    }

    if (error.message.includes('Client ID is required') || error.message.includes('Campaign ID is required')) {
      return res.status(400).json({
        error: 'Invalid request parameters',
        message: error.message
      });
    }

    // Generic server error for unexpected issues
    res.status(500).json({
      error: 'Failed to retrieve discovery results',
      message: error.message,
      campaignId: req.params.campaignId
    });
  }
});

export default router;
